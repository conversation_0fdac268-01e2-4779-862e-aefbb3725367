# Define parameters
$serverName = "LOUSQLWDS3005"
$databaseName = "MAAModels2"
$outputDir = ".\gerated_scripts"
$objectList = "fnSavedDFBaseClaimsMSBs,	fnGetBaseMSBsFactors,fnGetBaseMSBsCombined,spInsertMissingDFValues,spGetBaseData,spCalcPlanBase,spCalcInducedUtilizationFactors,PREP_spAppSyncDFData,spGetDFDataQuery,SCT_spCrosswalkFFSPercents,Trend_spSyncCostAndUse,SavedDFClaims,LkpIntFusionLine,CalcPlanBase,LkpIntDemog"  # Comma-separated list of object names

# Load SMO
[System.Reflection.Assembly]::LoadWithPartialName("Microsoft.SqlServer.SMO") | Out-Null

# Connect to server and database
$server = New-Object Microsoft.SqlServer.Management.Smo.Server $serverName
$database = $server.Databases[$databaseName]

# Split object names
$objectNames = $objectList -split ','

# Script each object
foreach ($name in $objectNames) {
    $obj = $database.Tables[$name] 
    if (-not $obj) { $obj = $database.Views[$name] }
    if (-not $obj) { $obj = $database.StoredProcedures[$name] }

    if ($obj) {
        $scripter = New-Object Microsoft.SqlServer.Management.Smo.Scripter ($server)
        $scripter.Options.ScriptDrops = $false
        $scripter.Options.WithDependencies = $false
        $scripter.Options.IncludeHeaders = $true
        $scripter.Options.ToFileOnly = $true
        $scripter.Options.FileName = Join-Path $outputDir "$name.sql"
        $scripter.Script($obj)
        Write-Host "Scripted $name to $outputDir\dbo.$name.sql"
    } else {
        Write-Host "Object $name not found in database."
    }
}
